"use client";

import { useState } from 'react';

export default function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
    setIsOpen(false);
  };

  return (
    <div className="md:hidden">
      {/* 汉堡菜单按钮 */}
      <button
        onClick={toggleMenu}
        className="p-2 text-muted-foreground hover:text-primary transition-colors"
        aria-label="切换菜单"
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>

      {/* 移动端菜单 */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 bg-secondary/95 backdrop-blur-sm border-b border-border/50 z-50">
          <div className="container mx-auto px-4 py-4">
            <div className="flex flex-col space-y-4">
              <button
                onClick={() => scrollToSection('game')}
                className="text-left text-muted-foreground hover:text-primary transition-colors py-2"
              >
                游戏
              </button>
              <button
                onClick={() => scrollToSection('features')}
                className="text-left text-muted-foreground hover:text-primary transition-colors py-2"
              >
                特色
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="text-left text-muted-foreground hover:text-primary transition-colors py-2"
              >
                关于
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
