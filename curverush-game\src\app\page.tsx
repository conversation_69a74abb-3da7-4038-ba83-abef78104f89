"use client";

import Image from "next/image";
import MobileMenu from "@/components/MobileMenu";

export default function Home() {
  return (
    <div className="min-h-screen gradient-bg">
      {/* 导航栏 */}
      <nav className="relative z-20 border-b border-border/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="text-xl font-bold text-gradient">Curve Rush</span>
            </div>
            <div className="flex items-center space-x-8">
              <div className="hidden md:flex items-center space-x-8">
                <a href="#game" className="text-muted-foreground hover:text-primary transition-colors">游戏</a>
                <a href="#features" className="text-muted-foreground hover:text-primary transition-colors">特色</a>
                <a href="#about" className="text-muted-foreground hover:text-primary transition-colors">关于</a>
              </div>
              <MobileMenu />
            </div>
          </div>
        </div>
      </nav>

      {/* Hero区域 */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-accent/10"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            <span className="text-gradient">Curve Rush</span>
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            在无尽的沙丘地形中体验终极冲刺！控制小球跳跃和滑行，挑战你的反应速度和技巧。
          </p>
          <button
            onClick={() => document.getElementById('game')?.scrollIntoView({ behavior: 'smooth' })}
            className="btn-primary text-lg px-8 py-4"
          >
            立即开始游戏
          </button>
        </div>
      </section>

      {/* 游戏区域 */}
      <section id="game" className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="card bg-black/50 backdrop-blur-sm">
              <div className="game-container h-[500px] md:h-[600px] lg:h-[700px]">
                <iframe
                  src="https://game.azgame.io/curve-rush/"
                  className="game-iframe w-full h-full"
                  allowFullScreen
                  title="Curve Rush Game"
                  sandbox="allow-scripts allow-same-origin allow-modals allow-pointer-lock allow-web-share allow-orientation-lock allow-screen-wake-lock allow-presentation allow-encrypted-media allow-autoplay allow-forms allow-popups allow-downloads allow-storage-access-by-user-activation allow-clipboard-write"
                  referrerPolicy="same-origin"
                  allow="autoplay; fullscreen; clipboard-write; gamepad"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 特色功能区域 */}
      <section id="features" className="py-20 bg-secondary/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gradient">
              探索Curve Rush的世界
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              体验物理引擎驱动的刺激游戏，在沙丘地形中展现你的技巧
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 特色1 */}
            <div className="card text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4">简单控制</h3>
              <p className="text-muted-foreground leading-relaxed">
                Curve Rush采用直观的控制方案，让各种技能水平的玩家都能轻松上手。使用鼠标点击、空格键或上箭头键控制小球的加速、冲刺和俯冲。
              </p>
            </div>

            {/* 特色2 */}
            <div className="card text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4">物理引擎</h3>
              <p className="text-muted-foreground leading-relaxed">
                体验真实的物理引擎，每次跳跃、飞行和着陆都遵循自然物理定律。时机掌握是游戏的精髓，完美的着陆将保持动量并获得更高分数。
              </p>
            </div>

            {/* 特色3 */}
            <div className="card text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4">全球排行榜</h3>
              <p className="text-muted-foreground leading-relaxed">
                通过全球排行榜系统与世界各地的玩家竞争。提交你的最高分数，挑战自己，争取在Curve Rush排行榜上获得更高的排名。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 游戏说明区域 */}
      <section id="about" className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gradient">
                掌握Curve Rush技巧
              </h2>
              <p className="text-xl text-muted-foreground">
                学习专业技巧，在沙丘冲刺中取得更好成绩
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
              <div className="card">
                <h3 className="text-2xl font-bold mb-4 text-primary">游戏玩法</h3>
                <p className="text-muted-foreground leading-relaxed mb-4">
                  在Curve Rush中，玩家控制一个小球在无尽的沙丘地形中水平移动。游戏采用真实的物理引擎，确保每次跳跃、飞行和着陆都遵循自然物理定律。
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  时机掌握是Curve Rush游戏的精髓。当小球滚下斜坡时，在正确的时机点击将使其飞向空中，而完美的着陆将保持动量并获得更高分数。
                </p>
              </div>

              <div className="card">
                <h3 className="text-2xl font-bold mb-4 text-primary">专业技巧</h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span>学习在斜坡上的最佳点击时机，实现最佳跳跃</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span>尽量在下坡着陆，这样可以保持动量并为下次跳跃做准备</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span>有时小而稳定的跳跃比追求最大高度更有效</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span>密切监控小球的运动轨迹，预测着陆位置</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span>培养稳定的游戏节奏，保持一致的表现</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="card bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20 text-center">
              <h3 className="text-2xl font-bold mb-4">为什么选择Curve Rush？</h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Curve Rush完美平衡了简单性和深度，创造了一种不可抗拒的游戏体验。游戏不需要复杂的操作说明，却提供了丰富的策略和技巧空间。
                精确跳跃、在空中滑翔和完美着陆的满足感形成了Curve Rush中不可抗拒的游戏循环。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="bg-secondary/50 border-t border-border/50 py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">C</span>
                </div>
                <span className="text-xl font-bold text-gradient">Curve Rush</span>
              </div>
              <p className="text-muted-foreground leading-relaxed">
                体验终极沙丘冲刺游戏，挑战你的反应速度和技巧。在无尽的沙丘地形中展现你的实力！
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-card-foreground">游戏特色</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>简单直观的控制</li>
                <li>真实物理引擎</li>
                <li>全球排行榜</li>
                <li>无尽挑战模式</li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-card-foreground">开始游戏</h4>
              <p className="text-muted-foreground mb-4">
                立即开始你的Curve Rush冒险之旅，看看你能在全球排行榜上排到第几名！
              </p>
              <button
                onClick={() => document.getElementById('game')?.scrollIntoView({ behavior: 'smooth' })}
                className="btn-primary"
              >
                开始游戏
              </button>
            </div>
          </div>

          <div className="border-t border-border/50 mt-8 pt-8 text-center">
            <p className="text-muted-foreground">
              © 2025 Curve Rush. 保留所有权利。
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
