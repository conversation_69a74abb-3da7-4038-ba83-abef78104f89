{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sea/curverush/curverush-game/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport MobileMenu from \"@/components/MobileMenu\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen gradient-bg\">\n      {/* 导航栏 */}\n      <nav className=\"relative z-20 border-b border-border/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">C</span>\n              </div>\n              <span className=\"text-xl font-bold text-gradient\">Curve Rush</span>\n            </div>\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"#game\" className=\"text-muted-foreground hover:text-primary transition-colors\">游戏</a>\n              <a href=\"#features\" className=\"text-muted-foreground hover:text-primary transition-colors\">特色</a>\n              <a href=\"#about\" className=\"text-muted-foreground hover:text-primary transition-colors\">关于</a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero区域 */}\n      <section className=\"relative py-20 overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-accent/10\"></div>\n        <div className=\"container mx-auto px-4 text-center relative z-10\">\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6\">\n            <span className=\"text-gradient\">Curve Rush</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto\">\n            在无尽的沙丘地形中体验终极冲刺！控制小球跳跃和滑行，挑战你的反应速度和技巧。\n          </p>\n          <button\n            onClick={() => document.getElementById('game')?.scrollIntoView({ behavior: 'smooth' })}\n            className=\"btn-primary text-lg px-8 py-4\"\n          >\n            立即开始游戏\n          </button>\n        </div>\n      </section>\n\n      {/* 游戏区域 */}\n      <section id=\"game\" className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"card bg-black/50 backdrop-blur-sm\">\n              <iframe\n                src=\"https://curverush.org/play/\"\n                className=\"game-iframe w-full h-[500px] md:h-[600px] lg:h-[700px]\"\n                allowFullScreen\n                title=\"Curve Rush Game\"\n                sandbox=\"allow-scripts allow-same-origin allow-modals allow-pointer-lock allow-web-share allow-orientation-lock allow-screen-wake-lock allow-presentation allow-encrypted-media allow-autoplay allow-forms allow-popups allow-downloads allow-storage-access-by-user-activation allow-clipboard-write\"\n                referrerPolicy=\"same-origin\"\n                allow=\"autoplay; fullscreen; clipboard-write\"\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 特色功能区域 */}\n      <section id=\"features\" className=\"py-20 bg-secondary/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-4 text-gradient\">\n              探索Curve Rush的世界\n            </h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              体验物理引擎驱动的刺激游戏，在沙丘地形中展现你的技巧\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {/* 特色1 */}\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4\">简单控制</h3>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                Curve Rush采用直观的控制方案，让各种技能水平的玩家都能轻松上手。使用鼠标点击、空格键或上箭头键控制小球的加速、冲刺和俯冲。\n              </p>\n            </div>\n\n            {/* 特色2 */}\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4\">物理引擎</h3>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                体验真实的物理引擎，每次跳跃、飞行和着陆都遵循自然物理定律。时机掌握是游戏的精髓，完美的着陆将保持动量并获得更高分数。\n              </p>\n            </div>\n\n            {/* 特色3 */}\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4\">全球排行榜</h3>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                通过全球排行榜系统与世界各地的玩家竞争。提交你的最高分数，挑战自己，争取在Curve Rush排行榜上获得更高的排名。\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 游戏说明区域 */}\n      <section id=\"about\" className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-4 text-gradient\">\n                掌握Curve Rush技巧\n              </h2>\n              <p className=\"text-xl text-muted-foreground\">\n                学习专业技巧，在沙丘冲刺中取得更好成绩\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n              <div className=\"card\">\n                <h3 className=\"text-2xl font-bold mb-4 text-primary\">游戏玩法</h3>\n                <p className=\"text-muted-foreground leading-relaxed mb-4\">\n                  在Curve Rush中，玩家控制一个小球在无尽的沙丘地形中水平移动。游戏采用真实的物理引擎，确保每次跳跃、飞行和着陆都遵循自然物理定律。\n                </p>\n                <p className=\"text-muted-foreground leading-relaxed\">\n                  时机掌握是Curve Rush游戏的精髓。当小球滚下斜坡时，在正确的时机点击将使其飞向空中，而完美的着陆将保持动量并获得更高分数。\n                </p>\n              </div>\n\n              <div className=\"card\">\n                <h3 className=\"text-2xl font-bold mb-4 text-primary\">专业技巧</h3>\n                <ul className=\"space-y-3 text-muted-foreground\">\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>学习在斜坡上的最佳点击时机，实现最佳跳跃</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>尽量在下坡着陆，这样可以保持动量并为下次跳跃做准备</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>有时小而稳定的跳跃比追求最大高度更有效</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>密切监控小球的运动轨迹，预测着陆位置</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>培养稳定的游戏节奏，保持一致的表现</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"card bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20 text-center\">\n              <h3 className=\"text-2xl font-bold mb-4\">为什么选择Curve Rush？</h3>\n              <p className=\"text-lg text-muted-foreground leading-relaxed\">\n                Curve Rush完美平衡了简单性和深度，创造了一种不可抗拒的游戏体验。游戏不需要复杂的操作说明，却提供了丰富的策略和技巧空间。\n                精确跳跃、在空中滑翔和完美着陆的满足感形成了Curve Rush中不可抗拒的游戏循环。\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 页脚 */}\n      <footer className=\"bg-secondary/50 border-t border-border/50 py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold\">C</span>\n                </div>\n                <span className=\"text-xl font-bold text-gradient\">Curve Rush</span>\n              </div>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                体验终极沙丘冲刺游戏，挑战你的反应速度和技巧。在无尽的沙丘地形中展现你的实力！\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 text-card-foreground\">游戏特色</h4>\n              <ul className=\"space-y-2 text-muted-foreground\">\n                <li>简单直观的控制</li>\n                <li>真实物理引擎</li>\n                <li>全球排行榜</li>\n                <li>无尽挑战模式</li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 text-card-foreground\">开始游戏</h4>\n              <p className=\"text-muted-foreground mb-4\">\n                立即开始你的Curve Rush冒险之旅，看看你能在全球排行榜上排到第几名！\n              </p>\n              <button\n                onClick={() => document.getElementById('game')?.scrollIntoView({ behavior: 'smooth' })}\n                className=\"btn-primary\"\n              >\n                开始游戏\n              </button>\n            </div>\n          </div>\n\n          <div className=\"border-t border-border/50 mt-8 pt-8 text-center\">\n            <p className=\"text-muted-foreground\">\n              © 2025 Curve Rush. 保留所有权利。\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAA6D;;;;;;kDACvF,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAA6D;;;;;;kDAC3F,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhG,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAmE;;;;;;0CAGhF,8OAAC;gCACC,SAAS,IAAM,SAAS,cAAc,CAAC,SAAS,eAAe;wCAAE,UAAU;oCAAS;gCACpF,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAI;gCACJ,WAAU;gCACV,eAAe;gCACf,OAAM;gCACN,SAAQ;gCACR,gBAAe;gCACf,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAMvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAMvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7D,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAA6C;;;;;;0DAG1D,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAKvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;8DAEzC,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAKvD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CACC,SAAS,IAAM,SAAS,cAAc,CAAC,SAAS,eAAe;oDAAE,UAAU;gDAAS;4CACpF,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sea/curverush/curverush-game/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sea/curverush/curverush-game/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}