@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #3b82f6;
  --secondary: #1e293b;
  --accent: #8b5cf6;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --border: #334155;
  --muted: #64748b;
  --muted-foreground: #94a3b8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-border: var(--border);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

/* 游戏iframe样式 */
.game-iframe {
  border: none;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  min-height: 400px;
  background: #000;
  overflow: hidden;
}

.game-iframe:hover {
  box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.25);
}

/* 游戏容器优化 */
.game-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  background: #000;
}

/* 确保iframe完全填充容器 */
.game-container iframe {
  display: block;
  width: 100% !important;
  height: 100% !important;
  border: none;
  background: transparent;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .game-iframe {
    border-radius: 8px;
    min-height: 350px;
  }

  .card {
    padding: 16px;
  }

  .btn-primary {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #0a0a0a 0%, #1e293b 50%, #0a0a0a 100%);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--accent));
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* 卡片样式 */
.card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.card:hover {
  border-color: var(--primary);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
}

/* 文本渐变 */
.text-gradient {
  background: linear-gradient(135deg, var(--primary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
