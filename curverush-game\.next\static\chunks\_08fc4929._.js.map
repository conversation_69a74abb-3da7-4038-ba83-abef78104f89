{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sea/curverush/curverush-game/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport MobileMenu from \"@/components/MobileMenu\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen gradient-bg\">\n      {/* 导航栏 */}\n      <nav className=\"relative z-20 border-b border-border/50 backdrop-blur-sm\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">C</span>\n              </div>\n              <span className=\"text-xl font-bold text-gradient\">Curve Rush</span>\n            </div>\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <a href=\"#game\" className=\"text-muted-foreground hover:text-primary transition-colors\">游戏</a>\n              <a href=\"#features\" className=\"text-muted-foreground hover:text-primary transition-colors\">特色</a>\n              <a href=\"#about\" className=\"text-muted-foreground hover:text-primary transition-colors\">关于</a>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero区域 */}\n      <section className=\"relative py-20 overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-accent/10\"></div>\n        <div className=\"container mx-auto px-4 text-center relative z-10\">\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6\">\n            <span className=\"text-gradient\">Curve Rush</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto\">\n            在无尽的沙丘地形中体验终极冲刺！控制小球跳跃和滑行，挑战你的反应速度和技巧。\n          </p>\n          <button\n            onClick={() => document.getElementById('game')?.scrollIntoView({ behavior: 'smooth' })}\n            className=\"btn-primary text-lg px-8 py-4\"\n          >\n            立即开始游戏\n          </button>\n        </div>\n      </section>\n\n      {/* 游戏区域 */}\n      <section id=\"game\" className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"card bg-black/50 backdrop-blur-sm\">\n              <iframe\n                src=\"https://curverush.org/play/\"\n                className=\"game-iframe w-full h-[500px] md:h-[600px] lg:h-[700px]\"\n                allowFullScreen\n                title=\"Curve Rush Game\"\n                sandbox=\"allow-scripts allow-same-origin allow-modals allow-pointer-lock allow-web-share allow-orientation-lock allow-screen-wake-lock allow-presentation allow-encrypted-media allow-autoplay allow-forms allow-popups allow-downloads allow-storage-access-by-user-activation allow-clipboard-write\"\n                referrerPolicy=\"same-origin\"\n                allow=\"autoplay; fullscreen; clipboard-write\"\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 特色功能区域 */}\n      <section id=\"features\" className=\"py-20 bg-secondary/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-4 text-gradient\">\n              探索Curve Rush的世界\n            </h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              体验物理引擎驱动的刺激游戏，在沙丘地形中展现你的技巧\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {/* 特色1 */}\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4\">简单控制</h3>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                Curve Rush采用直观的控制方案，让各种技能水平的玩家都能轻松上手。使用鼠标点击、空格键或上箭头键控制小球的加速、冲刺和俯冲。\n              </p>\n            </div>\n\n            {/* 特色2 */}\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4\">物理引擎</h3>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                体验真实的物理引擎，每次跳跃、飞行和着陆都遵循自然物理定律。时机掌握是游戏的精髓，完美的着陆将保持动量并获得更高分数。\n              </p>\n            </div>\n\n            {/* 特色3 */}\n            <div className=\"card text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold mb-4\">全球排行榜</h3>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                通过全球排行榜系统与世界各地的玩家竞争。提交你的最高分数，挑战自己，争取在Curve Rush排行榜上获得更高的排名。\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 游戏说明区域 */}\n      <section id=\"about\" className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-4 text-gradient\">\n                掌握Curve Rush技巧\n              </h2>\n              <p className=\"text-xl text-muted-foreground\">\n                学习专业技巧，在沙丘冲刺中取得更好成绩\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n              <div className=\"card\">\n                <h3 className=\"text-2xl font-bold mb-4 text-primary\">游戏玩法</h3>\n                <p className=\"text-muted-foreground leading-relaxed mb-4\">\n                  在Curve Rush中，玩家控制一个小球在无尽的沙丘地形中水平移动。游戏采用真实的物理引擎，确保每次跳跃、飞行和着陆都遵循自然物理定律。\n                </p>\n                <p className=\"text-muted-foreground leading-relaxed\">\n                  时机掌握是Curve Rush游戏的精髓。当小球滚下斜坡时，在正确的时机点击将使其飞向空中，而完美的着陆将保持动量并获得更高分数。\n                </p>\n              </div>\n\n              <div className=\"card\">\n                <h3 className=\"text-2xl font-bold mb-4 text-primary\">专业技巧</h3>\n                <ul className=\"space-y-3 text-muted-foreground\">\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>学习在斜坡上的最佳点击时机，实现最佳跳跃</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>尽量在下坡着陆，这样可以保持动量并为下次跳跃做准备</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>有时小而稳定的跳跃比追求最大高度更有效</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>密切监控小球的运动轨迹，预测着陆位置</span>\n                  </li>\n                  <li className=\"flex items-start\">\n                    <span className=\"text-accent mr-2\">•</span>\n                    <span>培养稳定的游戏节奏，保持一致的表现</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"card bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20 text-center\">\n              <h3 className=\"text-2xl font-bold mb-4\">为什么选择Curve Rush？</h3>\n              <p className=\"text-lg text-muted-foreground leading-relaxed\">\n                Curve Rush完美平衡了简单性和深度，创造了一种不可抗拒的游戏体验。游戏不需要复杂的操作说明，却提供了丰富的策略和技巧空间。\n                精确跳跃、在空中滑翔和完美着陆的满足感形成了Curve Rush中不可抗拒的游戏循环。\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* 页脚 */}\n      <footer className=\"bg-secondary/50 border-t border-border/50 py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold\">C</span>\n                </div>\n                <span className=\"text-xl font-bold text-gradient\">Curve Rush</span>\n              </div>\n              <p className=\"text-muted-foreground leading-relaxed\">\n                体验终极沙丘冲刺游戏，挑战你的反应速度和技巧。在无尽的沙丘地形中展现你的实力！\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 text-card-foreground\">游戏特色</h4>\n              <ul className=\"space-y-2 text-muted-foreground\">\n                <li>简单直观的控制</li>\n                <li>真实物理引擎</li>\n                <li>全球排行榜</li>\n                <li>无尽挑战模式</li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4 text-card-foreground\">开始游戏</h4>\n              <p className=\"text-muted-foreground mb-4\">\n                立即开始你的Curve Rush冒险之旅，看看你能在全球排行榜上排到第几名！\n              </p>\n              <button\n                onClick={() => document.getElementById('game')?.scrollIntoView({ behavior: 'smooth' })}\n                className=\"btn-primary\"\n              >\n                开始游戏\n              </button>\n            </div>\n          </div>\n\n          <div className=\"border-t border-border/50 mt-8 pt-8 text-center\">\n            <p className=\"text-muted-foreground\">\n              © 2025 Curve Rush. 保留所有权利。\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAA6D;;;;;;kDACvF,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAA6D;;;;;;kDAC3F,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhG,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAAmE;;;;;;0CAGhF,6LAAC;gCACC,SAAS;wCAAM;4CAAA,2BAAA,SAAS,cAAc,CAAC,qBAAxB,+CAAA,yBAAiC,cAAc,CAAC;wCAAE,UAAU;oCAAS;;gCACpF,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAI;gCACJ,WAAU;gCACV,eAAe;gCACf,OAAM;gCACN,SAAQ;gCACR,gBAAe;gCACf,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAKjE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAMvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAMvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7D,6LAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAK/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;0DAG1D,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAKvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;8DAEzC,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAKvD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CACC,SAAS;oDAAM;wDAAA,2BAAA,SAAS,cAAc,CAAC,qBAAxB,+CAAA,yBAAiC,cAAc,CAAC;oDAAE,UAAU;gDAAS;;4CACpF,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;KAlOwB", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sea/curverush/curverush-game/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Sea/curverush/curverush-game/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}